<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SITE_NAME; ?> - منصة المنيوهات الرقمية للمطاعم</title>
    <meta name="description" content="منصة Menuz لإنشاء منيوهات رقمية احترافية للمطاعم مع نظام الطلبات والدفع الإلكتروني">
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        /* Hero Section - تصميم عصري */
        .hero {
            background: var(--gradient-primary);
            color: white;
            padding: 8rem 0 6rem;
            text-align: center;
            position: relative;
            overflow: hidden;
            min-height: 90vh;
            display: flex;
            align-items: center;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .hero-content {
            position: relative;
            z-index: 2;
            max-width: 800px;
            margin: 0 auto;
        }

        .hero h1 {
            font-size: 4rem;
            font-weight: 800;
            margin-bottom: 1.5rem;
            text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            line-height: 1.1;
            letter-spacing: -0.02em;
            background: linear-gradient(45deg, #ffffff, #f8fafc);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero p {
            font-size: 1.4rem;
            margin-bottom: 3rem;
            opacity: 0.95;
            line-height: 1.6;
            font-weight: 400;
        }

        .hero-buttons {
            display: flex;
            gap: 1.5rem;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 4rem;
        }

        .hero-stats {
            display: flex;
            justify-content: center;
            gap: 3rem;
            margin-top: 4rem;
            flex-wrap: wrap;
        }

        .hero-stat {
            text-align: center;
        }

        .hero-stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            display: block;
            margin-bottom: 0.5rem;
        }

        .hero-stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
            text-transform: uppercase;
            letter-spacing: 0.1em;
        }
        
        /* Features Section - تصميم عصري */
        .features {
            padding: 8rem 0;
            background: var(--bg-secondary);
            position: relative;
        }

        .features::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 100px;
            background: linear-gradient(to bottom, white, transparent);
        }

        .section-title {
            text-align: center;
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 1rem;
            color: var(--text-dark);
            letter-spacing: -0.02em;
        }

        .section-subtitle {
            text-align: center;
            font-size: 1.2rem;
            color: var(--text-medium);
            margin-bottom: 4rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .feature-card {
            text-align: center;
            padding: 3rem 2rem;
            background: white;
            border-radius: 20px;
            box-shadow: var(--shadow);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
        }

        .feature-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-xl);
        }

        .feature-icon {
            width: 90px;
            height: 90px;
            background: var(--gradient-primary);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 2rem;
            font-size: 2.5rem;
            color: white;
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
        }

        .feature-card:hover .feature-icon {
            transform: scale(1.1) rotate(5deg);
            background: var(--gradient-secondary);
        }

        .feature-title {
            font-size: 1.6rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: var(--text-dark);
        }

        .feature-description {
            color: var(--text-medium);
            line-height: 1.7;
            font-size: 1rem;
        }
        
        /* Pricing Section - تصميم عصري */
        .pricing {
            padding: 8rem 0;
            background: white;
            position: relative;
        }

        .pricing::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 10% 20%, rgba(99, 102, 241, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 90% 80%, rgba(236, 72, 153, 0.05) 0%, transparent 50%);
        }

        .pricing-container {
            position: relative;
            z-index: 1;
        }

        .pricing-card {
            background: white;
            border-radius: 24px;
            padding: 3rem 2.5rem;
            text-align: center;
            position: relative;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 2px solid var(--border-light);
            box-shadow: var(--shadow);
        }

        .pricing-card:hover {
            transform: translateY(-12px);
            box-shadow: var(--shadow-xl);
            border-color: var(--primary-light);
        }

        .pricing-card.featured {
            border: 3px solid var(--primary-color);
            transform: scale(1.05);
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.02) 0%, rgba(236, 72, 153, 0.02) 100%);
            position: relative;
        }

        .pricing-card.featured::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: var(--gradient-primary);
            border-radius: 24px 24px 0 0;
        }

        .pricing-badge {
            position: absolute;
            top: -18px;
            left: 50%;
            transform: translateX(-50%);
            background: var(--gradient-primary);
            color: white;
            padding: 0.7rem 2rem;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 700;
            box-shadow: var(--shadow-md);
            letter-spacing: 0.05em;
        }

        .pricing-plan {
            font-size: 1.8rem;
            font-weight: 800;
            margin-bottom: 1rem;
            color: var(--text-dark);
        }

        .pricing-price {
            font-size: 3.5rem;
            font-weight: 800;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
            line-height: 1;
        }

        .pricing-period {
            color: var(--text-medium);
            margin-bottom: 2.5rem;
            font-size: 1.1rem;
        }

        .pricing-features {
            list-style: none;
            margin-bottom: 2.5rem;
            text-align: right;
        }

        .pricing-features li {
            padding: 0.8rem 0;
            color: var(--text-dark);
            position: relative;
            padding-right: 2rem;
            font-size: 1rem;
            border-bottom: 1px solid var(--border-light);
        }

        .pricing-features li:last-child {
            border-bottom: none;
        }

        .pricing-features li::before {
            content: '✓';
            position: absolute;
            right: 0;
            top: 0.8rem;
            color: var(--success-color);
            font-weight: bold;
            font-size: 1.2rem;
            width: 20px;
            height: 20px;
            background: rgba(16, 185, 129, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
        }
        
        /* CTA Section - تصميم عصري */
        .cta {
            padding: 8rem 0;
            background: var(--gradient-tertiary);
            color: white;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .cta::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 30% 70%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
        }

        .cta-content {
            position: relative;
            z-index: 1;
            max-width: 700px;
            margin: 0 auto;
        }

        .cta h2 {
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 1.5rem;
            line-height: 1.2;
        }

        .cta p {
            font-size: 1.3rem;
            margin-bottom: 3rem;
            opacity: 0.95;
            line-height: 1.6;
        }

        /* Footer - تصميم عصري محسن */
        .footer {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            color: white;
            padding: 5rem 0 2rem;
            position: relative;
            overflow: hidden;
        }

        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
        }

        .footer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 20%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(236, 72, 153, 0.1) 0%, transparent 50%);
            pointer-events: none;
        }

        .footer-content {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 1fr;
            gap: 3rem;
            margin-bottom: 3rem;
            position: relative;
            z-index: 1;
        }

        .footer-section h3 {
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: #f8fafc;
            position: relative;
        }

        .footer-section h3::after {
            content: '';
            position: absolute;
            bottom: -8px;
            right: 0;
            width: 40px;
            height: 3px;
            background: var(--gradient-primary);
            border-radius: 2px;
        }

        .footer-section p {
            color: #cbd5e1;
            line-height: 1.7;
            margin-bottom: 1.5rem;
            font-size: 0.95rem;
        }

        .footer-section ul {
            list-style: none;
        }

        .footer-section ul li {
            margin-bottom: 0.75rem;
        }

        .footer-section ul li a {
            color: #cbd5e1;
            text-decoration: none;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.5rem 0;
            border-radius: 6px;
            font-size: 0.9rem;
        }

        .footer-section ul li a:hover {
            color: #f8fafc;
            background: rgba(99, 102, 241, 0.1);
            transform: translateX(8px);
            padding-right: 0.5rem;
        }

        .footer-brand {
            margin-bottom: 2rem;
        }

        .footer-brand .logo {
            font-size: 2rem;
            font-weight: 800;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
        }

        .footer-social {
            display: flex;
            gap: 1rem;
            margin-top: 1.5rem;
        }

        .footer-social a {
            width: 45px;
            height: 45px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #cbd5e1;
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 1.2rem;
        }

        .footer-social a:hover {
            background: var(--gradient-primary);
            color: white;
            transform: translateY(-3px);
            box-shadow: var(--shadow-md);
        }

        .footer-bottom {
            text-align: center;
            padding-top: 2.5rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            color: #94a3b8;
            position: relative;
            z-index: 1;
        }

        .footer-bottom p {
            font-size: 0.9rem;
            margin: 0;
        }
        
        /* تحسينات للأجهزة المحمولة */
        @media (max-width: 768px) {
            .navbar {
                flex-direction: column;
                gap: 1rem;
                padding: 1rem 0;
            }

            .nav-links {
                flex-direction: column;
                gap: 1rem;
                width: 100%;
            }

            .nav-buttons {
                justify-content: center;
                width: 100%;
            }

            .hero {
                padding: 6rem 0 4rem;
                min-height: 80vh;
            }

            .hero h1 {
                font-size: 2.8rem;
                margin-bottom: 1rem;
            }

            .hero p {
                font-size: 1.2rem;
                margin-bottom: 2rem;
            }

            .hero-buttons {
                flex-direction: column;
                align-items: center;
                gap: 1rem;
            }

            .hero-stats {
                gap: 2rem;
                margin-top: 3rem;
            }

            .hero-stat-number {
                font-size: 2rem;
            }

            .section-title {
                font-size: 2.2rem;
            }

            .features {
                padding: 6rem 0;
            }

            .pricing {
                padding: 6rem 0;
            }

            .pricing-card.featured {
                transform: none;
                margin-top: 0;
            }

            .cta h2 {
                font-size: 2.2rem;
            }

            .cta p {
                font-size: 1.1rem;
            }

            .footer-content {
                grid-template-columns: 1fr;
                gap: 2.5rem;
            }

            .footer-social {
                justify-content: center;
            }
        }

        @media (max-width: 480px) {
            .hero h1 {
                font-size: 2.2rem;
            }

            .hero p {
                font-size: 1.1rem;
            }

            .btn-lg {
                padding: 0.875rem 1.5rem;
                font-size: 1rem;
            }

            .hero-stats {
                flex-direction: column;
                gap: 1.5rem;
            }

            .section-title {
                font-size: 1.8rem;
            }

            .feature-icon {
                width: 70px;
                height: 70px;
                font-size: 2rem;
            }

            .pricing-price {
                font-size: 2.8rem;
            }
        }

        /* تأثيرات إضافية */
        .fade-in {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease;
        }

        .fade-in.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .scale-on-hover {
            transition: transform 0.3s ease;
        }

        .scale-on-hover:hover {
            transform: scale(1.02);
        }
    </style>
</head>
<body>
    <!-- الهيدر -->
    <header class="header">
        <div class="container">
            <nav class="navbar">
                <div class="logo gradient-text">🍽️ <?php echo SITE_NAME; ?></div>
                <ul class="nav-links">
                    <li><a href="#features" class="nav-link-item">المميزات</a></li>
                    <li><a href="#pricing" class="nav-link-item">الأسعار</a></li>
                    <li><a href="#contact" class="nav-link-item">اتصل بنا</a></li>
                    <li class="nav-buttons">
                        <a href="auth/register.php" class="btn btn-outline btn-sm">إنشاء حساب</a>
                        <a href="auth/login.php" class="btn btn-primary btn-sm">تسجيل الدخول</a>
                    </li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- القسم الرئيسي -->
    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <h1>🍽️ منيوهات رقمية احترافية لمطعمك</h1>
                <p>أنشئ منيو رقمية جذابة وعصرية لمطعمك مع نظام الطلبات والدفع الإلكتروني في دقائق معدودة</p>
                <div class="hero-buttons">
                    <a href="auth/register.php" class="btn btn-primary btn-lg btn-glow">ابدأ مجاناً الآن</a>
                    <a href="#features" class="btn btn-secondary btn-lg hover-lift">اكتشف المميزات</a>
                </div>

                <div class="hero-stats">
                    <div class="hero-stat">
                        <span class="hero-stat-number">500+</span>
                        <span class="hero-stat-label">مطعم يثق بنا</span>
                    </div>
                    <div class="hero-stat">
                        <span class="hero-stat-number">50K+</span>
                        <span class="hero-stat-label">طلب شهرياً</span>
                    </div>
                    <div class="hero-stat">
                        <span class="hero-stat-number">99.9%</span>
                        <span class="hero-stat-label">وقت تشغيل</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- المميزات -->
    <section id="features" class="features">
        <div class="container">
            <h2 class="section-title">لماذا تختار <?php echo SITE_NAME; ?>؟</h2>
            <p class="section-subtitle">نقدم لك أفضل الحلول التقنية لتطوير مطعمك وزيادة أرباحك مع تجربة عملاء استثنائية</p>
            <div class="grid grid-3">
                <div class="feature-card fade-in-up hover-lift">
                    <div class="feature-icon">📱</div>
                    <h3 class="feature-title">منيو رقمية متجاوبة</h3>
                    <p class="feature-description">منيوهات تعمل بشكل مثالي على جميع الأجهزة مع تصميم عصري وجذاب يناسب هوية مطعمك</p>
                </div>

                <div class="feature-card fade-in-up hover-lift">
                    <div class="feature-icon">🔗</div>
                    <h3 class="feature-title">QR Code مخصص</h3>
                    <p class="feature-description">كود QR مخصص وأنيق لمطعمك يمكن طباعته على الطاولات والمواد التسويقية بجودة عالية</p>
                </div>

                <div class="feature-card fade-in-up hover-lift">
                    <div class="feature-icon">🛒</div>
                    <h3 class="feature-title">نظام طلبات ذكي</h3>
                    <p class="feature-description">استقبل الطلبات مباشرة من العملاء مع إشعارات فورية ونظام إدارة طلبات متطور</p>
                </div>

                <div class="feature-card fade-in-up hover-lift">
                    <div class="feature-icon">💳</div>
                    <h3 class="feature-title">دفع إلكتروني آمن</h3>
                    <p class="feature-description">ادعم الدفع الإلكتروني الآمن عبر PayPal وبطاقات الائتمان مع حماية كاملة للبيانات</p>
                </div>

                <div class="feature-card fade-in-up hover-lift">
                    <div class="feature-icon">📊</div>
                    <h3 class="feature-title">تحليلات متقدمة</h3>
                    <p class="feature-description">تتبع أداء مطعمك مع تقارير تفصيلية وإحصائيات ذكية عن المبيعات والعملاء</p>
                </div>

                <div class="feature-card fade-in-up hover-lift">
                    <div class="feature-icon">🌐</div>
                    <h3 class="feature-title">دعم متعدد اللغات</h3>
                    <p class="feature-description">منيوهات باللغة العربية والإنجليزية مع إمكانية إضافة لغات أخرى لخدمة جميع العملاء</p>
                </div>
            </div>
        </div>
    </section>

    <!-- الأسعار -->
    <section id="pricing" class="pricing">
        <div class="container pricing-container">
            <h2 class="section-title">اختر الباقة المناسبة لك</h2>
            <p class="section-subtitle">باقات مرنة تناسب جميع أحجام المطاعم مع إمكانية الترقية في أي وقت</p>
            <div class="grid grid-3">
                <!-- باقة Basic -->
                <div class="pricing-card">
                    <h3 class="pricing-plan">🌟 أساسية</h3>
                    <div class="pricing-price">$3</div>
                    <div class="pricing-period">شهرياً</div>
                    <ul class="pricing-features">
                        <li>منيو واحدة احترافية</li>
                        <li>حتى 15 منتج</li>
                        <li>QR Code عالي الجودة</li>
                        <li>دعم اللغتين (عربي/إنجليزي)</li>
                        <li>طلبات داخل المطعم</li>
                        <li>دعم فني عبر الإيميل</li>
                        <li>تحديثات مجانية</li>
                    </ul>
                    <a href="auth/register.php" class="btn btn-primary w-100 btn-glow">🚀 ابدأ الآن</a>
                </div>

                <!-- باقة Pro -->
                <div class="pricing-card featured hover-lift">
                    <div class="pricing-badge">⭐ الأكثر شعبية</div>
                    <h3 class="pricing-plan">💎 احترافية</h3>
                    <div class="pricing-price">$6</div>
                    <div class="pricing-period">شهرياً</div>
                    <ul class="pricing-features">
                        <li>منيو واحدة متطورة</li>
                        <li>حتى 50 منتج</li>
                        <li>QR Code مخصص</li>
                        <li>دعم اللغتين المتقدم</li>
                        <li>طلبات داخلية وخارجية</li>
                        <li>إحصائيات تفصيلية</li>
                        <li>دعم فني سريع</li>
                        <li>تخصيص الألوان</li>
                    </ul>
                    <a href="auth/register.php" class="btn btn-primary w-100 btn-glow pulse">🎯 ابدأ الآن</a>
                </div>

                <!-- باقة Ultra -->
                <div class="pricing-card hover-lift">
                    <h3 class="pricing-plan">👑 متقدمة</h3>
                    <div class="pricing-price">$8</div>
                    <div class="pricing-period">شهرياً</div>
                    <ul class="pricing-features">
                        <li>منيو واحدة فاخرة</li>
                        <li>منتجات غير محدودة</li>
                        <li>QR Code مخصص بالكامل</li>
                        <li>تصميم مخصص حسب الطلب</li>
                        <li>دعم متعدد اللغات</li>
                        <li>طلبات متقدمة</li>
                        <li>دفع إلكتروني آمن</li>
                        <li>تحليلات ذكية</li>
                        <li>دعم VIP مخصص</li>
                    </ul>
                    <a href="auth/register.php" class="btn btn-primary w-100 btn-glow">💫 ابدأ الآن</a>
                </div>
            </div>
        </div>
    </section>

    <!-- دعوة للعمل -->
    <section class="cta">
        <div class="container">
            <div class="cta-content">
                <h2>🚀 جاهز لتطوير مطعمك؟</h2>
                <p>انضم إلى آلاف المطاعم التي تستخدم <?php echo SITE_NAME; ?> لتحسين تجربة عملائها وزيادة أرباحها</p>
                <a href="auth/register.php" class="btn btn-primary btn-lg btn-glow">ابدأ مجاناً الآن</a>
            </div>
        </div>
    </section>

    <!-- الفوتر -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <!-- معلومات الشركة -->
                <div class="footer-section">
                    <div class="footer-brand">
                        <div class="logo">🍽️ <?php echo SITE_NAME; ?></div>
                        <p>منصة المنيوهات الرقمية الأولى في المنطقة العربية. نساعد المطاعم على تطوير تجربة عملائها وزيادة أرباحها من خلال حلول تقنية متطورة.</p>
                    </div>

                    <div class="footer-social">
                        <a href="#" title="فيسبوك">📘</a>
                        <a href="#" title="تويتر">🐦</a>
                        <a href="#" title="إنستغرام">📷</a>
                        <a href="#" title="لينكد إن">💼</a>
                        <a href="#" title="يوتيوب">📺</a>
                    </div>
                </div>

                <!-- روابط سريعة -->
                <div class="footer-section">
                    <h3>روابط سريعة</h3>
                    <ul>
                        <li><a href="#features">المميزات</a></li>
                        <li><a href="#pricing">الأسعار</a></li>
                        <li><a href="auth/register.php">إنشاء حساب</a></li>
                        <li><a href="auth/login.php">تسجيل الدخول</a></li>
                        <li><a href="#demo">عرض تجريبي</a></li>
                    </ul>
                </div>

                <!-- الدعم والمساعدة -->
                <div class="footer-section">
                    <h3>الدعم والمساعدة</h3>
                    <ul>
                        <li><a href="support.php">مركز المساعدة</a></li>
                        <li><a href="contact.php">اتصل بنا</a></li>
                        <li><a href="faq.php">الأسئلة الشائعة</a></li>
                        <li><a href="tutorials.php">دروس تعليمية</a></li>
                        <li><a href="api-docs.php">وثائق API</a></li>
                    </ul>
                </div>

                <!-- معلومات التواصل -->
                <div class="footer-section">
                    <h3>تواصل معنا</h3>
                    <ul>
                        <li><a href="mailto:<EMAIL>"><EMAIL></a></li>
                        <li><a href="tel:+966501234567">+966 50 123 4567</a></li>
                        <li><a href="https://www.menuz.com" target="_blank">www.menuz.com</a></li>
                        <li><a href="#chat">دردشة مباشرة</a></li>
                        <li><a href="terms.php">الشروط والأحكام</a></li>
                    </ul>
                </div>
            </div>

            <div class="footer-bottom">
                <p>&copy; 2024 <?php echo SITE_NAME; ?>. جميع الحقوق محفوظة. صُنع بـ ❤️ في المملكة العربية السعودية</p>
            </div>
        </div>
    </footer>

    <script src="assets/js/main.js"></script>
</body>
</html>
