// JavaScript رئيسي للموقع

document.addEventListener('DOMContentLoaded', function() {
    // تهيئة الموقع
    initializeApp();

    // تهيئة تأثيرات الصفحة الرئيسية
    initializeHomePageEffects();
});

/**
 * تهيئة التطبيق
 */
function initializeApp() {
    // إخفاء التنبيهات تلقائياً
    autoHideAlerts();
    
    // تهيئة النماذج
    initializeForms();
    
    // تهيئة القوائم المنسدلة
    initializeDropdowns();
    
    // تهيئة المودال
    initializeModals();
}

/**
 * إخفاء التنبيهات تلقائياً
 */
function autoHideAlerts() {
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            alert.style.opacity = '0';
            setTimeout(() => {
                alert.remove();
            }, 300);
        }, 5000);
    });
}

/**
 * تهيئة النماذج
 */
function initializeForms() {
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!validateForm(this)) {
                e.preventDefault();
            }
        });
    });
}

/**
 * التحقق من صحة النماذج
 */
function validateForm(form) {
    let isValid = true;
    const requiredFields = form.querySelectorAll('[required]');
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            showFieldError(field, 'هذا الحقل مطلوب');
            isValid = false;
        } else {
            clearFieldError(field);
        }
    });
    
    // التحقق من البريد الإلكتروني
    const emailFields = form.querySelectorAll('input[type="email"]');
    emailFields.forEach(field => {
        if (field.value && !isValidEmail(field.value)) {
            showFieldError(field, 'البريد الإلكتروني غير صحيح');
            isValid = false;
        }
    });
    
    // التحقق من كلمة المرور
    const passwordFields = form.querySelectorAll('input[type="password"]');
    if (passwordFields.length > 1) {
        const password = passwordFields[0].value;
        const confirmPassword = passwordFields[1].value;
        
        if (password !== confirmPassword) {
            showFieldError(passwordFields[1], 'كلمة المرور غير متطابقة');
            isValid = false;
        }
    }
    
    return isValid;
}

/**
 * عرض خطأ في الحقل
 */
function showFieldError(field, message) {
    clearFieldError(field);
    
    field.style.borderColor = '#f44336';
    
    const errorDiv = document.createElement('div');
    errorDiv.className = 'field-error';
    errorDiv.style.color = '#f44336';
    errorDiv.style.fontSize = '0.875rem';
    errorDiv.style.marginTop = '0.25rem';
    errorDiv.textContent = message;
    
    field.parentNode.appendChild(errorDiv);
}

/**
 * إزالة خطأ الحقل
 */
function clearFieldError(field) {
    field.style.borderColor = '';
    
    const existingError = field.parentNode.querySelector('.field-error');
    if (existingError) {
        existingError.remove();
    }
}

/**
 * التحقق من صحة البريد الإلكتروني
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * تهيئة القوائم المنسدلة
 */
function initializeDropdowns() {
    const dropdowns = document.querySelectorAll('.dropdown');
    dropdowns.forEach(dropdown => {
        const toggle = dropdown.querySelector('.dropdown-toggle');
        const menu = dropdown.querySelector('.dropdown-menu');
        
        if (toggle && menu) {
            toggle.addEventListener('click', function(e) {
                e.preventDefault();
                menu.classList.toggle('show');
            });
            
            // إغلاق القائمة عند النقر خارجها
            document.addEventListener('click', function(e) {
                if (!dropdown.contains(e.target)) {
                    menu.classList.remove('show');
                }
            });
        }
    });
}

/**
 * تهيئة المودال
 */
function initializeModals() {
    const modalTriggers = document.querySelectorAll('[data-modal]');
    modalTriggers.forEach(trigger => {
        trigger.addEventListener('click', function(e) {
            e.preventDefault();
            const modalId = this.getAttribute('data-modal');
            const modal = document.getElementById(modalId);
            if (modal) {
                showModal(modal);
            }
        });
    });
    
    // إغلاق المودال
    const closeButtons = document.querySelectorAll('.modal-close');
    closeButtons.forEach(button => {
        button.addEventListener('click', function() {
            const modal = this.closest('.modal');
            if (modal) {
                hideModal(modal);
            }
        });
    });
    
    // إغلاق المودال عند النقر على الخلفية
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        modal.addEventListener('click', function(e) {
            if (e.target === this) {
                hideModal(this);
            }
        });
    });
}

/**
 * عرض المودال
 */
function showModal(modal) {
    modal.style.display = 'flex';
    document.body.style.overflow = 'hidden';
    
    setTimeout(() => {
        modal.classList.add('show');
    }, 10);
}

/**
 * إخفاء المودال
 */
function hideModal(modal) {
    modal.classList.remove('show');
    
    setTimeout(() => {
        modal.style.display = 'none';
        document.body.style.overflow = '';
    }, 300);
}

/**
 * عرض تنبيه
 */
function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type}`;
    alertDiv.textContent = message;
    
    const container = document.querySelector('.container') || document.body;
    container.insertBefore(alertDiv, container.firstChild);
    
    // إخفاء التنبيه تلقائياً
    setTimeout(() => {
        alertDiv.style.opacity = '0';
        setTimeout(() => {
            alertDiv.remove();
        }, 300);
    }, 5000);
}

/**
 * تحميل المحتوى بـ AJAX
 */
function loadContent(url, container, callback) {
    fetch(url)
        .then(response => response.text())
        .then(data => {
            if (container) {
                container.innerHTML = data;
            }
            if (callback) {
                callback(data);
            }
        })
        .catch(error => {
            console.error('خطأ في تحميل المحتوى:', error);
            showAlert('حدث خطأ في تحميل المحتوى', 'error');
        });
}

/**
 * إرسال نموذج بـ AJAX
 */
function submitForm(form, callback) {
    const formData = new FormData(form);
    
    fetch(form.action, {
        method: form.method || 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (callback) {
            callback(data);
        }
    })
    .catch(error => {
        console.error('خطأ في إرسال النموذج:', error);
        showAlert('حدث خطأ في إرسال البيانات', 'error');
    });
}

/**
 * تنسيق الأرقام
 */
function formatNumber(number) {
    return new Intl.NumberFormat('ar-SA').format(number);
}

/**
 * تنسيق العملة
 */
function formatCurrency(amount, currency = 'USD') {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: currency
    }).format(amount);
}

/**
 * نسخ النص إلى الحافظة
 */
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showAlert('تم نسخ النص بنجاح', 'success');
    }).catch(() => {
        showAlert('فشل في نسخ النص', 'error');
    });
}

/**
 * تبديل اللغة
 */
function toggleLanguage() {
    const currentLang = document.documentElement.lang || 'ar';
    const newLang = currentLang === 'ar' ? 'en' : 'ar';
    
    // تحديث اتجاه الصفحة
    if (newLang === 'ar') {
        document.documentElement.dir = 'rtl';
        document.body.style.textAlign = 'right';
    } else {
        document.documentElement.dir = 'ltr';
        document.body.style.textAlign = 'left';
    }
    
    document.documentElement.lang = newLang;
    
    // حفظ اللغة في التخزين المحلي
    localStorage.setItem('language', newLang);
    
    // إعادة تحميل الصفحة لتطبيق اللغة الجديدة
    location.reload();
}

// تحميل اللغة المحفوظة
document.addEventListener('DOMContentLoaded', function() {
    const savedLang = localStorage.getItem('language');
    if (savedLang && savedLang !== document.documentElement.lang) {
        toggleLanguage();
    }
});

/**
 * تهيئة تأثيرات الصفحة الرئيسية
 */
function initializeHomePageEffects() {
    // تأثير الظهور التدريجي
    initializeFadeInAnimation();

    // تأثير الهيدر عند التمرير
    initializeHeaderScrollEffect();

    // تأثير العد التصاعدي للإحصائيات
    initializeCounterAnimation();

    // تأثير التمرير السلس
    initializeSmoothScrolling();

    // تأثير الجسيمات في الخلفية
    initializeParticleEffect();
}

/**
 * تأثير الظهور التدريجي للعناصر
 */
function initializeFadeInAnimation() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('visible');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    // إضافة كلاس fade-in للعناصر
    const elementsToAnimate = document.querySelectorAll('.feature-card, .pricing-card, .section-title, .section-subtitle');
    elementsToAnimate.forEach((el, index) => {
        el.classList.add('fade-in');
        el.style.transitionDelay = `${index * 0.1}s`;
        observer.observe(el);
    });
}

/**
 * تأثير الهيدر عند التمرير
 */
function initializeHeaderScrollEffect() {
    const header = document.querySelector('.header');
    if (!header) return;

    let lastScrollY = window.scrollY;

    window.addEventListener('scroll', () => {
        const currentScrollY = window.scrollY;

        if (currentScrollY > 100) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }

        // إخفاء/إظهار الهيدر حسب اتجاه التمرير
        if (currentScrollY > lastScrollY && currentScrollY > 200) {
            header.style.transform = 'translateY(-100%)';
        } else {
            header.style.transform = 'translateY(0)';
        }

        lastScrollY = currentScrollY;
    });
}

/**
 * تأثير العد التصاعدي للإحصائيات
 */
function initializeCounterAnimation() {
    const counters = document.querySelectorAll('.hero-stat-number');

    const animateCounter = (counter) => {
        const target = parseInt(counter.textContent.replace(/[^\d]/g, ''));
        const duration = 2000;
        const step = target / (duration / 16);
        let current = 0;

        const timer = setInterval(() => {
            current += step;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }

            // تنسيق الرقم حسب النوع
            const originalText = counter.textContent;
            if (originalText.includes('%')) {
                counter.textContent = Math.floor(current) + '%';
            } else if (originalText.includes('K')) {
                counter.textContent = Math.floor(current / 1000) + 'K+';
            } else {
                counter.textContent = Math.floor(current) + '+';
            }
        }, 16);
    };

    // مراقبة ظهور الإحصائيات
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCounter(entry.target);
                observer.unobserve(entry.target);
            }
        });
    });

    counters.forEach(counter => observer.observe(counter));
}

/**
 * تأثير التمرير السلس
 */
function initializeSmoothScrolling() {
    const links = document.querySelectorAll('a[href^="#"]');

    links.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();

            const targetId = link.getAttribute('href');
            const targetElement = document.querySelector(targetId);

            if (targetElement) {
                const headerHeight = document.querySelector('.header')?.offsetHeight || 0;
                const targetPosition = targetElement.offsetTop - headerHeight - 20;

                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        });
    });
}

/**
 * تأثير الجسيمات في الخلفية
 */
function initializeParticleEffect() {
    const hero = document.querySelector('.hero');
    if (!hero) return;

    // إنشاء canvas للجسيمات
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    canvas.style.position = 'absolute';
    canvas.style.top = '0';
    canvas.style.left = '0';
    canvas.style.width = '100%';
    canvas.style.height = '100%';
    canvas.style.pointerEvents = 'none';
    canvas.style.opacity = '0.3';
    hero.appendChild(canvas);

    let particles = [];

    function resizeCanvas() {
        canvas.width = hero.offsetWidth;
        canvas.height = hero.offsetHeight;
    }

    function createParticle() {
        return {
            x: Math.random() * canvas.width,
            y: Math.random() * canvas.height,
            size: Math.random() * 3 + 1,
            speedX: (Math.random() - 0.5) * 0.5,
            speedY: (Math.random() - 0.5) * 0.5,
            opacity: Math.random() * 0.5 + 0.2
        };
    }

    function initParticles() {
        particles = [];
        for (let i = 0; i < 50; i++) {
            particles.push(createParticle());
        }
    }

    function updateParticles() {
        particles.forEach(particle => {
            particle.x += particle.speedX;
            particle.y += particle.speedY;

            if (particle.x < 0 || particle.x > canvas.width) particle.speedX *= -1;
            if (particle.y < 0 || particle.y > canvas.height) particle.speedY *= -1;
        });
    }

    function drawParticles() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        particles.forEach(particle => {
            ctx.beginPath();
            ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
            ctx.fillStyle = `rgba(255, 255, 255, ${particle.opacity})`;
            ctx.fill();
        });

        // رسم خطوط الاتصال بين الجسيمات القريبة
        for (let i = 0; i < particles.length; i++) {
            for (let j = i + 1; j < particles.length; j++) {
                const dx = particles[i].x - particles[j].x;
                const dy = particles[i].y - particles[j].y;
                const distance = Math.sqrt(dx * dx + dy * dy);

                if (distance < 100) {
                    ctx.beginPath();
                    ctx.moveTo(particles[i].x, particles[i].y);
                    ctx.lineTo(particles[j].x, particles[j].y);
                    ctx.strokeStyle = `rgba(255, 255, 255, ${0.1 * (1 - distance / 100)})`;
                    ctx.lineWidth = 1;
                    ctx.stroke();
                }
            }
        }
    }

    function animate() {
        updateParticles();
        drawParticles();
        requestAnimationFrame(animate);
    }

    resizeCanvas();
    initParticles();
    animate();

    window.addEventListener('resize', () => {
        resizeCanvas();
        initParticles();
    });
}
